/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"log"
	"net"
	"os"
	"path/filepath"
	"runtime"
	"strconv"

	"github.com/containernetworking/cni/pkg/skel"
	current "github.com/containernetworking/cni/pkg/types/100"
	"github.com/containernetworking/cni/pkg/version"
	"github.com/containernetworking/plugins/pkg/ip"
	"github.com/containernetworking/plugins/pkg/ns"
	"github.com/vishvananda/netlink"
	"golang.org/x/sys/unix"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/ipam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/types"
	metadata "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/meta-data"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	typeswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/cnitypes"
	ipwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ip"
	ipamwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ipam"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
	netutilwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netutil"
	nswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ns"
	oswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/os"
)

const (
	logFile          = "/var/log/bci/bci-cni.log"
	PluginName       = "bci-cni"
	LogFileMaxSizeMB = "1500"

	toContainerRulePriority   = 1024
	fromContainerRulePriority = 2048

	// TODO: inject version when build
	BuildVersion = ""
)

func init() {
	// this ensures that main runs only on main thread (thread group leader).
	// since namespace ops (unshare, setns) are done for a single thread, we
	// must ensure that the goroutine does not jump from OS thread to thread
	runtime.LockOSThread()
}

type bciCniPlugin struct {
	os         oswrapper.Interface
	nlink      netlinkwrapper.Interface
	types      typeswrapper.Interface
	ip         ipwrapper.Interface
	ns         nswrapper.Interface
	ipam       ipamwrapper.Interface
	netutil    netutilwrapper.Interface
	ipamc      ipam.Interface
	metaclient metadata.Interface
}

func newBciCniPlugin() *bciCniPlugin {
	return &bciCniPlugin{
		os:         oswrapper.New(),
		nlink:      netlinkwrapper.New(),
		types:      typeswrapper.New(),
		ip:         ipwrapper.New(),
		ns:         nswrapper.New(),
		ipam:       ipamwrapper.New(),
		netutil:    netutilwrapper.New(),
		ipamc:      ipam.NewBciCniIpam(),
		metaclient: metadata.NewClient("", ""),
	}
}

func (p *bciCniPlugin) setupLogger() {
	logDir := filepath.Dir(logFile)
	if err := p.os.MkdirAll(logDir, 0755); err != nil && !os.IsExist(err) {
		log.Println(err)
		os.Exit(1)
	}
	initFlags()
}

func initFlags() {
	// init klog flagsets
	logger.InitFlags(nil)

	// set default value for bci-cni
	_ = flag.Set("logtostderr", "false")
	_ = flag.Set("log_file", logFile)
	_ = flag.Set("log_file_max_size", LogFileMaxSizeMB)

	// shoot
	flag.Parse()
}

func main() {
	plugin := newBciCniPlugin()

	plugin.setupLogger()
	defer logger.Flush()

	if e := skel.PluginMainWithError(plugin.cmdAdd, plugin.cmdCheck, plugin.cmdDel, version.All, buildString(PluginName)); e != nil {
		if err := e.Print(); err != nil {
			log.Print("Error writing error JSON to stdout: ", err)
		}
		logger.Flush()
		os.Exit(1)
	}
}

func (p *bciCniPlugin) cmdAdd(args *skel.CmdArgs) error {
	var (
		ctx      = logger.NewContext()
		n        *types.NetConf
		result   = &current.Result{}
		err      error
		netns    ns.NetNS
		eniNetns ns.NetNS
	)

	logger.Infof(ctx, "====> CmdAdd Begins <====")
	defer logger.Infof(ctx, "====> CmdAdd Ends <====")
	logger.Infof(ctx, "[cmdAdd]: containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
		args.ContainerID, args.Netns, args.IfName, args.Args, args.Path)

	n, cniVersion, err := types.LoadConf(args.StdinData)
	if err != nil {
		return fmt.Errorf("failed to load netconf: %w", err)
	}

	if n.PrevResult != nil {
		result, err = current.NewResultFromResult(n.PrevResult)
		if err != nil {
			return fmt.Errorf("could not convert result to current version: %w", err)
		}
	}

	k8sArgs, err := types.LoadK8sArgs(args.Args)
	if err != nil {
		return err
	}

	netns, err = p.ns.GetNS(args.Netns)
	if err != nil {
		return fmt.Errorf("failed to open netns %q: %w", args.Netns, err)
	}
	defer netns.Close()

	// ipam adds ip
	resp, err := p.ipamc.CmdAdd(ctx, args, k8sArgs)
	if err != nil {
		return fmt.Errorf("ipam exec CmdAdd failed: %w", err)
	}

	defer func() {
		if err != nil {
			logger.Errorf(ctx, "cmdAdd allocate ip error: %+v, execute CmdDel", err)
			_, _ = p.ipamc.CmdDel(ctx, args, k8sArgs)
		}
	}()

	if !resp.IsSuccess {
		err = fmt.Errorf("ipam backend allocates ip failed: %v", resp.ErrMsg)
		logger.Error(ctx, err.Error())
		return err
	}

	if resp.GetENIMultiIP() == nil {
		return errors.New("ipam backend returns empty response")
	}

	// 判断是否需要分配多个ip
	allocIPs := make([]net.IP, 0)
	var ipv6Address net.IP

	// 处理IPv4地址（多IP或单IP）
	if multiIPs := resp.GetENIMultiIP().GetMultiIPs(); multiIPs != nil {
		logger.Infof(ctx, "pod %s/%s allocated multi ip", k8sArgs.K8S_POD_NAMESPACE, k8sArgs.K8S_POD_NAME)
		for _, allocatedIP := range multiIPs {
			parseIP := net.ParseIP(allocatedIP)
			if parseIP == nil {
				return fmt.Errorf("ipam allocated ip %v format error", resp.GetENIMultiIP().GetMultiIPs())
			}
			allocIPs = append(allocIPs, parseIP)
		}
	} else {
		logger.Infof(ctx, "pod %s/%s allocated single ip", k8sArgs.K8S_POD_NAMESPACE, k8sArgs.K8S_POD_NAME)
		allocIP := net.ParseIP(resp.GetENIMultiIP().GetIP())
		if allocIP == nil {
			return fmt.Errorf("ipam allocated ip %v format error", resp.GetENIMultiIP().GetIP())
		}
		allocIPs = append(allocIPs, allocIP)
	}

	// 处理IPv6地址
	if ipv6IP := resp.GetENIMultiIP().GetIPv6IP(); ipv6IP != "" {
		logger.Infof(ctx, "pod %s/%s allocated ipv6 address", k8sArgs.K8S_POD_NAMESPACE, k8sArgs.K8S_POD_NAME)
		ipv6Address = net.ParseIP(ipv6IP)
		if ipv6Address == nil {
			return fmt.Errorf("ipam allocated ipv6 %v format error", ipv6IP)
		}
		// 验证确实是IPv6地址
		if ipv6Address.To4() != nil {
			return fmt.Errorf("ipam allocated ipv6 %v is not a valid IPv6 address", ipv6IP)
		}
		logger.Infof(ctx, "pod %s/%s allocated ipv6 is %s", k8sArgs.K8S_POD_NAMESPACE, k8sArgs.K8S_POD_NAME, ipv6Address.String())
	}

	logger.Infof(ctx, "pod %s/%s allocated ipv4 addresses: %+v, ipv6 address: %s",
		k8sArgs.K8S_POD_NAMESPACE, k8sArgs.K8S_POD_NAME, allocIPs,
		func() string {
			if ipv6Address != nil {
				return ipv6Address.String()
			}
			return "none"
		}())

	if len(allocIPs) == 0 {
		// 理论走不到
		logger.Errorf(ctx, "pod %s/%s allocated ip is nil, length of allocIPs is 0", k8sArgs.K8S_POD_NAMESPACE, k8sArgs.K8S_POD_NAME)
		return fmt.Errorf("ipam allocated ip is nil")
	}

	// containerd不识别多个ip，在返回值中只能显示单个ip（主要的IPv4地址）
	retIPConfig := make([]*current.IPConfig, 0)

	// 添加IPv4地址配置
	for idx, allocIP := range allocIPs {
		addrBits := 32
		if allocIP.To4() == nil {
			addrBits = 128
		}
		ipConfig := &current.IPConfig{
			Address: net.IPNet{
				IP:   allocIP,
				Mask: net.CIDRMask(addrBits, addrBits),
			},
			Gateway: net.ParseIP(resp.GetENIMultiIP().GetGateway()),
		}
		if idx == 0 {
			retIPConfig = append(retIPConfig, ipConfig)
		}
		result.IPs = append(result.IPs, ipConfig)
	}

	// 添加IPv6地址配置
	if ipv6Address != nil {
		var gateway net.IP
		// 尝试从meta-data获取IPv6网关
		gateway, err = p.getIPv6Gateway(ctx, resp.GetENIMultiIP().GetMac(), ipv6Address.String(), resp.GetENIMultiIP().GetGateway())
		if err != nil {
			logger.Warningf(ctx, "failed to get IPv6 gateway from meta-data, using fallback: %v", err)
			return err
		}

		ipv6Config := &current.IPConfig{
			Address: net.IPNet{
				IP:   ipv6Address,
				Mask: net.CIDRMask(128, 128),
			},
			Gateway: gateway,
		}
		result.IPs = append(result.IPs, ipv6Config)
		logger.Infof(ctx, "pod %s/%s added ipv6 config: %+v", k8sArgs.K8S_POD_NAMESPACE, k8sArgs.K8S_POD_NAME, ipv6Config)
	}

	eniNetns, err = p.ns.GetNS(resp.GetENIMultiIP().GetEniNetns())
	if err != nil {
		return fmt.Errorf("failed to open netns %q: %w", resp.GetENIMultiIP().GetEniNetns(), err)
	} else {
		logger.Infof(ctx, "eni netns is %+v", eniNetns.Path())
	}
	defer eniNetns.Close()

	// if mtu is not explicit specified, try to detect
	if n.MTU == nil {
		var (
			mtu int
		)

		err = eniNetns.Do(func(nn ns.NetNS) error {
			mtu, err = p.detectEniMtu()
			return err
		})
		if err != nil {
			return err
		}

		n.MTU = &mtu
		logger.Infof(ctx, "mtu is detected by eni: %v", *n.MTU)
	} else {
		logger.Infof(ctx, "mtu is explicit specified: %v", *n.MTU)
	}

	hostInterfaces, _, err := p.setupContainerVeth(ctx, netns, args.IfName, *n.MTU, result)
	if err != nil {
		logger.Infof(ctx, "setup container veth error: %+v", err)
		return err
	}

	err = p.setupEniNsVeth(ctx, hostInterfaces, eniNetns, result)
	if err != nil {
		logger.Infof(ctx, "setup eni network namespace error: %+v", err)
		return err
	}

	if resp.GetENIMultiIP().GetEniRequireUniqueRouteTable() {
		// 这里先不用兼容，pfs场景只支持单ip
		// 配置IPv4路由规则
		allocIP := allocIPs[0]
		// clean up old rule
		_ = p.delToOrFromContainerRule(true, allocIP)
		_ = p.delToOrFromContainerRule(false, allocIP)

		// add to container rule
		err = p.addToContainerRule(allocIP)
		if err != nil {
			return fmt.Errorf("failed to add to container rule for IPv4: %w", err)
		}

		// add from container rule
		err = p.addFromContainerRule(allocIP, hostInterfaces[0], int(resp.GetENIMultiIP().GetEniRouteTable()))
		if err != nil {
			return fmt.Errorf("failed to add from container rule for IPv4: %w", err)
		}

		// 配置IPv6路由规则（如果存在IPv6地址）
		if ipv6Address != nil {
			logger.Infof(ctx, "configuring IPv6 routing rules for address: %s", ipv6Address.String())
			// clean up old IPv6 rule
			_ = p.delToOrFromContainerRule(true, ipv6Address)
			_ = p.delToOrFromContainerRule(false, ipv6Address)

			// add to container rule for IPv6
			err = p.addToContainerRule(ipv6Address)
			if err != nil {
				return fmt.Errorf("failed to add to container rule for IPv6: %w", err)
			}

			// add from container rule for IPv6
			err = p.addFromContainerRule(ipv6Address, hostInterfaces[0], int(resp.GetENIMultiIP().GetEniRouteTable()))
			if err != nil {
				return fmt.Errorf("failed to add from container rule for IPv6: %w", err)
			}
			logger.Infof(ctx, "successfully configured IPv6 routing rules for address: %s", ipv6Address.String())
		}
	}
	result.IPs = retIPConfig
	return p.types.PrintResult(result, cniVersion)
}

func (p *bciCniPlugin) cmdDel(args *skel.CmdArgs) error {
	var (
		ctx = logger.NewContext()
		err error
	)

	logger.Infof(ctx, "====> CmdDel Begins <====")
	defer logger.Infof(ctx, "====> CmdDel Ends <====")
	logger.Infof(ctx, "[cmdDel]: containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
		args.ContainerID, args.Netns, args.IfName, args.Args, args.Path)
	defer func() {
		if err != nil {
			logger.Errorf(ctx, "CmdDel release ip error: %+v, containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
				err, args.ContainerID, args.Netns, args.IfName, args.Args, args.Path)
		}
	}()
	_, _, err = types.LoadConf(args.StdinData)
	if err != nil {
		return fmt.Errorf("failed to load netconf: %w", err)
	}

	k8sArgs, err := types.LoadK8sArgs(args.Args)
	if err != nil {
		return err
	}

	// ipam deletes ip
	resp, err := p.ipamc.CmdDel(ctx, args, k8sArgs)
	if err != nil {
		return fmt.Errorf("ipam exec CmdDel failed: %w", err)
	}

	if !resp.IsSuccess {
		msg := fmt.Sprintf("ipam backend releases ip failed: %v", resp.ErrMsg)
		logger.Error(ctx, msg)
		return errors.New(msg)
	}

	if args.Netns == "" {
		return nil
	}

	var (
		podIPv4Addr net.IP
		podIPv6Addr net.IP
	)
	err = p.ns.WithNetNSPath(args.Netns, func(_ ns.NetNS) error {
		var err error

		if resp.GetENIMultiIP().GetEniRequireUniqueRouteTable() {
			// 获取IPv4地址
			podIPv4Addr, err = p.getPodIPFromNetNS(args.IfName, netlink.FAMILY_V4)
			if err != nil {
				logger.Warningf(ctx, "failed to get IPv4 address from netns: %v", err)
			} else {
				logger.Infof(ctx, "found IPv4 address for deletion: %s", podIPv4Addr.String())
			}

			// 获取IPv6地址（如果存在）
			podIPv6Addr, err = p.getPodIPFromNetNS(args.IfName, netlink.FAMILY_V6)
			if err != nil {
				logger.Warningf(ctx, "failed to get IPv6 address from netns (may not exist): %v", err)
				podIPv6Addr = nil // 确保IPv6地址为空，避免后续处理错误
			} else {
				logger.Infof(ctx, "found IPv6 address for deletion: %s", podIPv6Addr.String())
			}
		}

		// 单pod多ip的场景，创建了多个路由表，删除每个ip对应的路由表
		// 删除IPv4路由规则
		ruleList, err := p.nlink.RuleList(netlink.FAMILY_V4)
		if err != nil {
			return fmt.Errorf("failed to list IPv4 rules: %w", err)
		}
		for _, rule := range ruleList {
			// 跳过 main、local 和 default 表
			if rule.Table == unix.RT_TABLE_MAIN || rule.Table == unix.RT_TABLE_LOCAL || rule.Table == unix.RT_TABLE_DEFAULT {
				continue
			}
			// 删除其他表中的规则
			logger.Infof(ctx, "delete IPv4 rule table: %v", rule.Table)
			err := p.nlink.RuleDel(&rule)
			if err != nil {
				return fmt.Errorf("failed to delete IPv4 rule: %v, error: %w", rule, err)
			}
		}

		// 删除IPv6路由规则
		ruleListV6, err := p.nlink.RuleList(netlink.FAMILY_V6)
		if err != nil {
			logger.Warningf(ctx, "failed to list IPv6 rules (may not be supported): %v", err)
		} else {
			for _, rule := range ruleListV6 {
				// 跳过 main、local 和 default 表
				if rule.Table == unix.RT_TABLE_MAIN || rule.Table == unix.RT_TABLE_LOCAL || rule.Table == unix.RT_TABLE_DEFAULT {
					continue
				}
				// 删除其他表中的规则
				logger.Infof(ctx, "delete IPv6 rule table: %v", rule.Table)
				err := p.nlink.RuleDel(&rule)
				if err != nil {
					return fmt.Errorf("failed to delete IPv6 rule: %v, error: %w", rule, err)
				}
			}
		}

		logger.Infof(ctx, "delete link device by name: %s", args.IfName)
		err = p.ip.DelLinkByName(args.IfName)
		if err != nil && err == ip.ErrLinkNotFound {
			return nil
		}
		return err
	})

	if err != nil {
		_, ok := err.(ns.NSPathNotExistErr)
		if ok {
			return nil
		}
		return err
	}

	if resp.GetENIMultiIP().GetEniRequireUniqueRouteTable() {
		// 删除IPv4路由规则
		if podIPv4Addr != nil {
			err = p.delToOrFromContainerRule(true, podIPv4Addr)
			if err != nil {
				return fmt.Errorf("failed to del IPv4 to container rule: %w", err)
			}

			err = p.delToOrFromContainerRule(false, podIPv4Addr)
			if err != nil {
				return fmt.Errorf("failed to del IPv4 from container rule: %w", err)
			}
			logger.Infof(ctx, "successfully deleted IPv4 routing rules for %s", podIPv4Addr.String())
		}

		// 删除IPv6路由规则（如果存在）
		if podIPv6Addr != nil {
			err = p.delToOrFromContainerRule(true, podIPv6Addr)
			if err != nil {
				return fmt.Errorf("failed to del IPv6 to container rule: %w", err)
			}

			err = p.delToOrFromContainerRule(false, podIPv6Addr)
			if err != nil {
				return fmt.Errorf("failed to del IPv6 from container rule: %w", err)
			}
			logger.Infof(ctx, "successfully deleted IPv6 routing rules for %s", podIPv6Addr.String())
		}
	}

	return nil
}

func (p *bciCniPlugin) cmdCheck(args *skel.CmdArgs) error {
	var (
		ctx = logger.NewContext()
		n   types.NetConf
	)

	logger.Infof(ctx, "====> CmdCheck Begins <====")
	defer logger.Infof(ctx, "====> CmdCheck Ends <====")
	logger.Infof(ctx, "[cmdCheck]: containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
		args.ContainerID, args.Netns, args.IfName, args.Args, args.Path)

	err := json.Unmarshal(args.StdinData, &n)
	if err != nil {
		return fmt.Errorf("failed to load netconf: %w", err)
	}

	// TODO: (chenyaqi) any need to ipam check?

	if n.NetConf.RawPrevResult == nil {
		return errors.New("bci-cni: Required prevResult missing")
	}

	err = version.ParsePrevResult(&n.NetConf)
	if err != nil {
		return err
	}

	result, err := current.NewResultFromResult(n.PrevResult)
	if err != nil {
		return err
	}

	var (
		contMap current.Interface
	)

	for _, intf := range result.Interfaces {
		if args.IfName == intf.Name {
			if args.Netns == intf.Sandbox {
				contMap = *intf
				continue
			}
		}
	}

	// The namespace must be the same as what was configured
	if args.Netns != contMap.Sandbox {
		return fmt.Errorf("sandbox in prevResult %s doesn't match configured netns: %s", contMap.Sandbox, args.Netns)
	}

	// TODO: (chenyaqi) check in container ns

	return nil
}

func (p *bciCniPlugin) setupContainerVeth(ctx context.Context, netns ns.NetNS, ifName string, mtu int, pr *current.Result) (
	[]*current.Interface, []*current.Interface, error) {
	hostInterfaces := make([]*current.Interface, 0)
	containerInterfaces := make([]*current.Interface, 0)

	err := netns.Do(func(hostNS ns.NetNS) error {
		// 单ip在容器内创建一个网卡，多ip在容器内创建多个网卡
		for idx, ipc := range pr.IPs {
			hostInterface := &current.Interface{}
			containerInterface := &current.Interface{}
			var targetIfName string
			if idx == 0 {
				targetIfName = ifName
			} else {
				targetIfName = "eth" + strconv.Itoa(idx)
			}
			logger.Infof(ctx, "create veth pair in container netns %+v, ifname: %s", netns.Path(), targetIfName)
			// 创建veth pair对
			hostVeth, contVeth0, err := p.ip.SetupVeth(targetIfName, mtu, "", hostNS)
			if err != nil {
				return err
			}
			hostInterface.Name = hostVeth.Name
			hostInterface.Mac = hostVeth.HardwareAddr.String()
			containerInterface.Name = contVeth0.Name
			containerInterface.Mac = contVeth0.HardwareAddr.String()
			containerInterface.Sandbox = netns.Path()
			ipc.Interface = current.Int(2*idx + 1)
			pr.Interfaces = append(pr.Interfaces, hostInterface, containerInterface)
			hostInterfaces = append(hostInterfaces, hostInterface)
			containerInterfaces = append(containerInterfaces, containerInterface)

			// 通过网卡名称获取网卡，验证网卡是否存在
			contVeth, err := p.netutil.InterfaceByName(targetIfName)
			if err != nil {
				return fmt.Errorf("failed to look up %q: %w", targetIfName, err)
			}
			// 配置网卡
			tmpResult := &current.Result{
				CNIVersion: pr.CNIVersion,
				Interfaces: pr.Interfaces,
				// 依次配置容器网卡，一个网卡设备配置一个ip
				IPs:    []*current.IPConfig{ipc},
				Routes: pr.Routes,
				DNS:    pr.DNS,
			}
			if err := p.ipam.ConfigureIface(targetIfName, tmpResult); err != nil {
				return err
			}

			// Delete the route that was automatically added (only for IPv4)
			// IPv6 addresses need to keep their automatically added host routes
			if ipc.Address.IP.To4() != nil {
				route := netlink.Route{
					LinkIndex: contVeth.Index,
					Dst: &net.IPNet{
						IP:   ipc.Address.IP.Mask(ipc.Address.Mask),
						Mask: ipc.Address.Mask,
					},
					Scope: netlink.SCOPE_NOWHERE,
				}
				if err := p.nlink.RouteDel(&route); err != nil && !netlinkwrapper.IsNotExistError(err) {
					return errorWithLog(ctx, fmt.Errorf("failed to delete IPv4 route %v in container netns: %w", route, err))
				}
				logger.Infof(ctx, "Deleted automatically added IPv4 route: %+v", route)
			} else {
				logger.Infof(ctx, "Keeping automatically added IPv6 host route for %s", ipc.Address.IP.String())
			}
			addrBits := 32
			if ipc.Address.IP.To4() == nil {
				addrBits = 128
			}

			// 配置main表的路由规则
			if idx == 0 || ipc.Address.IP.To4() == nil {
				// 确定使用的零地址类型（IPv4或IPv6）
				var zeroIP net.IP
				var ipVersion = "IPv4"
				if ipc.Address.IP.To4() != nil {
					zeroIP = net.IPv4zero
				} else {
					zeroIP = net.IPv6zero
					ipVersion = "IPv6"
				}

				for _, r := range []netlink.Route{
					{
						LinkIndex: contVeth.Index,
						Dst: &net.IPNet{
							IP:   ipc.Gateway,
							Mask: net.CIDRMask(addrBits, addrBits),
						},
						Scope:    netlink.SCOPE_LINK,
						Src:      ipc.Address.IP,
						Priority: 1000,
					},
					{
						LinkIndex: contVeth.Index,
						Dst: &net.IPNet{
							IP:   zeroIP,
							Mask: net.CIDRMask(0, addrBits),
						},
						Scope:    netlink.SCOPE_UNIVERSE,
						Gw:       ipc.Gateway,
						Src:      ipc.Address.IP,
						Priority: 1000,
					},
				} {
					logger.Infof(ctx, "Attempting to replace %s route in main table: %+v", ipVersion, r)
					if err := p.nlink.RouteReplace(&r); err != nil {
						logger.Errorf(ctx, "Failed to replace %s route %+v in main table: %v", ipVersion, r, err)
						return fmt.Errorf("failed to add %s route %v in container netns: %w", ipVersion, r, err)
					}
					logger.Infof(ctx, "%s Route replaced successfully in main table: %+v", ipVersion, r)
				}
			}

			// 多ip的场景，需要创建多个路由表，并更新每个路由表下的路由规则
			if len(pr.IPs) > 1 {
				logger.Infof(ctx, "netns %s has multi ips: %+v, need create extra route talbe", netns.Path(), pr.IPs)
				// 创建新的路由表，每个ip对应一个路由表
				// 执行效果：ip rule add from ************* table 100
				// 创建一个新的规则对象
				rule := p.nlink.NewRule()
				// 设置规则属性
				rule.Src = &net.IPNet{IP: ipc.Address.IP, Mask: ipc.Address.Mask}
				rule.Table = (idx + 1) * 100

				// 添加规则
				if err := p.nlink.RuleAdd(rule); err != nil {
					return fmt.Errorf("failed to create rule table for ip: %s", ipc.Address.IP.String())
				}
				// 在路由表中添加路由规则
				// 执行效果：ip route add *************** dev eth0 scope link src ************* table 100
				// 执行效果：ip route add default via *************** dev eth0 src ************* table 100
				// 对于IPv6：ip -6 route add ::/0 via gateway dev eth0 src ipv6_addr table 100

				// 确定使用的零地址类型（IPv4或IPv6）
				var zeroIP net.IP
				var ipVersion = "IPv4"
				if ipc.Address.IP.To4() != nil {
					zeroIP = net.IPv4zero
				} else {
					zeroIP = net.IPv6zero
					ipVersion = "IPv6"
				}

				for _, r := range []netlink.Route{
					{
						LinkIndex: contVeth.Index,
						Dst: &net.IPNet{
							IP:   ipc.Gateway,
							Mask: net.CIDRMask(addrBits, addrBits),
						},
						Scope:    netlink.SCOPE_LINK,
						Src:      ipc.Address.IP,
						Table:    rule.Table,
						Priority: rule.Table,
					},
					{
						LinkIndex: contVeth.Index,
						Dst: &net.IPNet{
							IP:   zeroIP,
							Mask: net.CIDRMask(0, addrBits),
						},
						Scope:    netlink.SCOPE_UNIVERSE,
						Gw:       ipc.Gateway,
						Src:      ipc.Address.IP,
						Table:    rule.Table,
						Priority: rule.Table,
					},
				} {
					logger.Infof(ctx, "Attempting to replace %v route: %+v", ipVersion, r)
					if err := p.nlink.RouteReplace(&r); err != nil {
						logger.Errorf(ctx, "Failed to replace %v route %+v: %v", ipVersion, r, err)
						return fmt.Errorf("failed to add %v route %v in container netns: %w", ipVersion, r, err)
					}
					logger.Infof(ctx, "%v Route replaced successfully: %+v", ipVersion, r)
				}
			}
		}
		return nil
	})
	if err != nil {
		return nil, nil, err
	}

	return hostInterfaces, containerInterfaces, nil
}

func (p *bciCniPlugin) setupEniNsVeth(ctx context.Context, hostInterfaces []*current.Interface, eniNs ns.NetNS, result *current.Result) error {
	vethMap := make(map[string]netlink.Link)
	for _, hostInterface := range hostInterfaces {
		vethName := hostInterface.Name
		veth, err := p.nlink.LinkByName(vethName)
		if err != nil {
			return fmt.Errorf("failed to lookup %q: %w", vethName, err)
		}
		// 把网卡放到eni的network命名空间下
		err = p.nlink.LinkSetNsFd(veth, int(eniNs.Fd()))
		if err != nil {
			return fmt.Errorf("failed to set %q netns: %w", vethName, err)
		}
		vethMap[vethName] = veth
	}

	err := eniNs.Do(func(hostNS ns.NetNS) error {
		logger.Infof(ctx, "begin setup eni netns %+v", eniNs.Path())
		for _, ipc := range result.IPs {
			maskLen := 128
			ipVersion := "IPv6"
			if ipc.Address.IP.To4() != nil {
				maskLen = 32
				ipVersion = "IPv4"
			}
			logger.Infof(ctx, "configuring %s address %s in eni netns", ipVersion, ipc.Address.IP.String())

			// 获取容器ip对应的host veth
			// containerInterface := result.Interfaces[*ipc.Interface]
			hostInterface := result.Interfaces[*ipc.Interface-1]
			veth, ok := vethMap[hostInterface.Name]
			if !ok {
				return fmt.Errorf("failed to find veth device %v in netns %v", hostInterface.Name, eniNs.Path())
			}
			// 点亮网卡
			err := p.nlink.LinkSetUp(veth)
			if err != nil {
				return fmt.Errorf("failed to set %v up in netns %v: %w", veth.Attrs().Name, eniNs.Path(), err)
			}
			// 设置网卡的ip地址为默认网关的地址（如果有网关）
			ipGateway := &net.IPNet{
				IP:   ipc.Gateway,
				Mask: net.CIDRMask(maskLen, maskLen),
			}
			addr := &netlink.Addr{IPNet: ipGateway, Label: ""}
			if err = p.nlink.AddrAdd(veth, addr); err != nil {
				return fmt.Errorf("failed to add %s gateway addr (%#v) to veth: %w", ipVersion, ipGateway, err)
			}
			logger.Infof(ctx, "successfully added %s gateway address %s to veth %s", ipVersion, ipc.Gateway.String(), veth.Attrs().Name)
			// 删除veth接口上自动生成的scope link路由（如果有网关）
			err = p.delVethScopeLinkRoute(ctx, veth, ipc.Gateway, maskLen)
			if err != nil {
				logger.Warningf(ctx, "failed to delete auto-generated scope link route for %s gateway %s on veth %s: %v", ipVersion, ipc.Gateway.String(), veth.Attrs().Name, err)
			} else {
				logger.Infof(ctx, "successfully deleted auto-generated scope link route for %s gateway %s on veth %s", ipVersion, ipc.Gateway.String(), veth.Attrs().Name)
			}

			// 设置弹性网卡到容器的路由规则
			ipn := &net.IPNet{
				IP:   ipc.Address.IP,
				Mask: net.CIDRMask(maskLen, maskLen),
			}
			// dst happens to be the same as IP/net of host veth
			if err = p.ip.AddHostRoute(ipn, nil, veth); err != nil {
				return errorWithLog(ctx, fmt.Errorf("failed to add %s route in eniNs: %w", ipVersion, err))
			}
			logger.Infof(ctx, "successfully added %s host route %s in eni netns", ipVersion, ipn.String())
		}

		err := p.ip.EnableForward(result.IPs)
		if err != nil {
			return fmt.Errorf("failed to enable ip forward in netns %v: %w", eniNs.Path(), err)
		}
		logger.Infof(ctx, "end setup eni netns %+v", eniNs.Path())
		return nil
	})

	if err != nil {
		return err
	}
	return nil
}

// delVethScopeLinkRoute 删除veth接口上自动生成的scope link路由
func (p *bciCniPlugin) delVethScopeLinkRoute(ctx context.Context, veth netlink.Link, gateway net.IP, maskLen int) error {
	// 构造需要删除的路由：网关地址的网段路由
	dst := &net.IPNet{
		IP:   gateway.Mask(net.CIDRMask(maskLen, maskLen)),
		Mask: net.CIDRMask(maskLen, maskLen),
	}

	route := &netlink.Route{
		Dst:       dst,
		Scope:     netlink.SCOPE_LINK,
		LinkIndex: veth.Attrs().Index,
	}

	err := p.nlink.RouteDel(route)
	if err != nil && !netlinkwrapper.IsNotExistError(err) {
		return fmt.Errorf("failed to delete scope link route %v: %w", route, err)
	}

	logger.Infof(ctx, "successfully deleted scope link route %v from veth %s", route, veth.Attrs().Name)
	return nil
}
func buildString(pluginName string) string {
	return fmt.Sprintf("CNI %s plugin %s", pluginName, BuildVersion)
}

func (p *bciCniPlugin) detectEniMtu() (int, error) {
	intf, err := p.netutil.DetectDefaultRouteInterfaceName()
	if err != nil {
		return 0, fmt.Errorf("failed to detect default interface name: %w", err)
	}

	mtu, err := p.netutil.DetectInterfaceMTU(intf)
	if err != nil {
		return 0, fmt.Errorf("failed to detect %v mtu: %w", intf, err)
	}

	return mtu, nil
}

func (p *bciCniPlugin) addToContainerRule(addr net.IP) error {
	rule := netlink.NewRule()
	rule.Table = 254
	rule.Priority = toContainerRulePriority

	addrBits := 32
	if addr.To4() == nil {
		addrBits = 128
	}

	rule.Dst = &net.IPNet{
		IP:   addr,
		Mask: net.CIDRMask(addrBits, addrBits),
	}

	err := p.nlink.RuleDel(rule)
	if err != nil && !netlinkwrapper.IsNotExistError(err) {
		return err
	}

	if err := p.nlink.RuleAdd(rule); err != nil {
		return err
	}

	return nil
}

func (p *bciCniPlugin) addFromContainerRule(addr net.IP, hostInterface *current.Interface, rtTable int) error {
	rule := netlink.NewRule()
	rule.Table = rtTable
	rule.Priority = fromContainerRulePriority
	rule.IifName = hostInterface.Name

	addrBits := 32
	if addr.To4() == nil {
		addrBits = 128
	}

	rule.Src = &net.IPNet{
		IP:   addr,
		Mask: net.CIDRMask(addrBits, addrBits),
	}

	err := p.nlink.RuleDel(rule)
	if err != nil && !netlinkwrapper.IsNotExistError(err) {
		return err
	}

	if err := p.nlink.RuleAdd(rule); err != nil {
		return err
	}

	return nil
}

func (p *bciCniPlugin) delToOrFromContainerRule(isToContainer bool, addr net.IP) error {
	rule := netlink.NewRule()

	addrBits := 32
	if addr.To4() == nil {
		addrBits = 128
	}

	if isToContainer {
		rule.Dst = &net.IPNet{
			IP:   addr,
			Mask: net.CIDRMask(addrBits, addrBits),
		}
	} else {
		rule.Src = &net.IPNet{
			IP:   addr,
			Mask: net.CIDRMask(addrBits, addrBits),
		}
	}

	err := p.nlink.RuleDel(rule)
	if err != nil && !netlinkwrapper.IsNotExistError(err) {
		return err
	}

	return nil
}

func (p *bciCniPlugin) getPodIPFromNetNS(ifName string, ipFamily int) (net.IP, error) {
	intf, err := p.nlink.LinkByName(ifName)
	if err != nil {
		return nil, err
	}

	addrs, err := p.nlink.AddrList(intf, ipFamily)
	if err != nil {
		return nil, err
	}
	if len(addrs) == 0 {
		return nil, fmt.Errorf("no ip found on %v", ifName)
	}

	ip := addrs[0].IP
	return ip, nil
}

func errorWithLog(ctx context.Context, err error) error {
	logger.Error(ctx, err)
	return err
}

// getIPv6Gateway 动态获取 IPv6 网关地址
func (p *bciCniPlugin) getIPv6Gateway(ctx context.Context, macAddress, ipv6Address, fallbackGateway string) (net.IP, error) {
	// 1. 首先尝试通过 meta-data API 获取 IPv6 网关
	if macAddress != "" && ipv6Address != "" {
		gateway, err := p.metaclient.GetLinkGateway(macAddress, ipv6Address)
		if err == nil && gateway != "" {
			parsedGateway := net.ParseIP(gateway)
			if parsedGateway != nil {
				// 验证返回的是 IPv6 网关
				if parsedGateway.To4() == nil {
					logger.Infof(ctx, "found IPv6 gateway %s from meta-data for IPv6 address %s", gateway, ipv6Address)
					return parsedGateway, nil
				} else {
					logger.Warningf(ctx, "meta-data returned IPv4 gateway %s for IPv6 address %s, ignoring", gateway, ipv6Address)
				}
			}
		} else {
			logger.Infof(ctx, "failed to get IPv6 gateway from meta-data for address %s: %v", ipv6Address, err)
		}
	}

	// 2. 检查 fallback 网关是否为 IPv6
	if fallbackGateway != "" {
		parsedFallback := net.ParseIP(fallbackGateway)
		if parsedFallback != nil && parsedFallback.To4() == nil {
			logger.Infof(ctx, "using fallback IPv6 gateway %s for IPv6 address %s", fallbackGateway, ipv6Address)
			return parsedFallback, nil
		} else {
			logger.Infof(ctx, "fallback gateway %s is IPv4, not suitable for IPv6 address %s", fallbackGateway, ipv6Address)
		}
	}

	// 3. 最后回退到标准的链路本地网关
	defaultGateway := net.ParseIP("fe80::1")
	logger.Infof(ctx, "using default link-local gateway fe80::1 for IPv6 address %s", ipv6Address)
	return defaultGateway, nil
}
