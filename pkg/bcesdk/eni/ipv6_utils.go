/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package eni

import (
	"fmt"
	"net"
	"strings"
)

// IsIPv6Address checks if the given IP address is a valid IPv6 address
func IsIPv6Address(ipAddr string) bool {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		return false
	}
	return ip.To4() == nil && ip.To16() != nil
}

// IsIPv4Address checks if the given IP address is a valid IPv4 address
func IsIPv4Address(ipAddr string) bool {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		return false
	}
	return ip.To4() != nil
}

// ValidateIPAddresses validates a list of IP addresses and separates them into IPv4 and IPv6
func ValidateIPAddresses(addresses []string) (ipv4Addrs []string, ipv6Addrs []string, err error) {
	for _, addr := range addresses {
		if addr == "" {
			// 空字符串表示自动分配，跳过验证
			continue
		}

		if IsIPv4Address(addr) {
			ipv4Addrs = append(ipv4Addrs, addr)
		} else if IsIPv6Address(addr) {
			ipv6Addrs = append(ipv6Addrs, addr)
		} else {
			return nil, nil, fmt.Errorf("invalid IP address format: %s", addr)
		}
	}
	return ipv4Addrs, ipv6Addrs, nil
}

// FormatIPv6Address normalizes IPv6 address format
func FormatIPv6Address(ipAddr string) (string, error) {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		return "", fmt.Errorf("invalid IP address: %s", ipAddr)
	}

	if ip.To4() != nil {
		return "", fmt.Errorf("not an IPv6 address: %s", ipAddr)
	}

	// 返回标准化的IPv6地址格式
	return ip.String(), nil
}

// SeparateIPsByVersion separates a mixed list of IPv4 and IPv6 addresses
func SeparateIPsByVersion(addresses []string) (ipv4List []string, ipv6List []string) {
	for _, addr := range addresses {
		if strings.Contains(addr, ":") {
			// 简单的IPv6检测（包含冒号）
			ipv6List = append(ipv6List, addr)
		} else {
			// IPv4地址
			ipv4List = append(ipv4List, addr)
		}
	}
	return ipv4List, ipv6List
}

// ValidatePrivateIPSet validates PrivateIP set for consistency
func ValidatePrivateIPSet(privateIPs []*PrivateIP, expectIPv6 bool) error {
	for i, ip := range privateIPs {
		if ip == nil {
			return fmt.Errorf("PrivateIP at index %d is nil", i)
		}

		if ip.PrivateIPAddress == "" {
			// 空地址表示自动分配，跳过IP版本检查
			continue
		}

		isIPv6 := IsIPv6Address(ip.PrivateIPAddress)
		if expectIPv6 && !isIPv6 {
			return fmt.Errorf("PrivateIP at index %d should be IPv6 but is IPv4: %s", i, ip.PrivateIPAddress)
		}
		if !expectIPv6 && isIPv6 {
			return fmt.Errorf("PrivateIP at index %d should be IPv4 but is IPv6: %s", i, ip.PrivateIPAddress)
		}
	}
	return nil
}
