package controllers

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"runtime"
	"syscall"
	"time"

	"github.com/containernetworking/plugins/pkg/ns"
	"github.com/vishvananda/netlink"
	"github.com/vishvananda/netns"
	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/enilink"
)

var (
	ADMINUSERNAME = "admin"
)

func (r *BciNodeReconciler) findENILinkByMac(macAddress string) (netlink.Link, error) {
	var eniIntf netlink.Link
	i := 0
	// list all interfaces, and find ENI by mac address
	for {
		if i >= 3 {
			return nil, fmt.Errorf("eni with mac address %v not found", macAddress)
		}

		interfaces, err := r.nlink.LinkList()
		if err != nil {
			return nil, fmt.Errorf("failed to list interfaces: %v", interfaces)
		}

		for _, intf := range interfaces {
			if intf.Attrs().HardwareAddr.String() == macAddress {
				eniIntf = intf
				break
			}
		}

		if eniIntf == nil {
			i = i + 1
			time.Sleep(3 * time.Second)
			continue
		}
		return eniIntf, nil
	}
}

func createEniNs(eniNsName string) error {
	runtime.LockOSThread()
	defer runtime.UnlockOSThread()
	origns, _ := netns.Get()
	defer origns.Close()
	eniNsHandle, err := netns.NewNamed(eniNsName)
	defer eniNsHandle.Close()
	if err != nil {
		return err
	}
	err = netns.Set(origns)
	if err != nil {
		return err
	}
	return nil
}

func delEniNs(eniNsName string) error {
	runtime.LockOSThread()
	defer runtime.UnlockOSThread()

	return netns.DeleteNamed(eniNsName)
}

func (r *BciNodeReconciler) HandleAdd(ctx context.Context, userAllocAdd networkingv1.AllocationMap) ([]string, error) {
	var errEniIDs []string
	for user, userEni := range userAllocAdd {
		if user == ADMINUSERNAME {
			continue
		}
		for eniID, eni := range userEni {
			if RequireUniqueRouteTable {
				if err := r.AddEniWithoutNetns(ctx, eni); err != nil {
					errEniIDs = append(errEniIDs, eniID)
					logger.Errorf(ctx, "Add eni %s without netns failed, message: %v", eniID, err)
				}
			} else {
				if error := r.AddEni(ctx, eni); error != nil {
					errEniIDs = append(errEniIDs, eniID)
					_ = r.DelEni(ctx, eniID)
					logger.Errorf(ctx, "Add eni %s failed, message: %v", eniID, error)
				}
			}
		}
	}
	if len(errEniIDs) > 0 {
		return errEniIDs, fmt.Errorf("eni ids %v init failed", errEniIDs)
	}
	return nil, nil
}

func (r *BciNodeReconciler) HandleDel(ctx context.Context, userAllocDel networkingv1.AllocationMap) error {
	for user, userEni := range userAllocDel {
		if user == ADMINUSERNAME {
			continue
		}
		for eniID, eni := range userEni {
			if RequireUniqueRouteTable {
				continue
			}
			if error := r.DelEni(ctx, eni.EniID); error != nil {
				logger.Errorf(ctx, "Del eni %s failed, message: %v", eniID, error)
				// not return, continue to del eni
			}
		}
	}
	return nil
}

func (r *BciNodeReconciler) AddEni(ctx context.Context, eni *networkingv1.AllocationEni) error {
	// use ns path to judge is ns exists
	// any better way ?
	nsPath := fmt.Sprintf("/run/netns/enins-%s", eni.EniID)
	if _, err := os.Stat(nsPath); err == nil || os.IsExist(err) {
		logger.Infof(ctx, "netns %s exists", nsPath)
		return nil
	}

	eniIntf, err := r.findENILinkByMac(eni.MacAddress)
	if err != nil {
		logger.Errorf(ctx, "find eni: %s , mac: %s error", eni.EniID, eni.MacAddress)
		return err
	}

	// create eni ns
	eniNsName := fmt.Sprintf("enins-%s", eni.EniID)
	err = createEniNs(eniNsName)
	if err != nil {
		logger.Errorf(ctx, "create eni ns error")
		return err
	}
	// get netns handler
	netns, err := ns.GetNS(nsPath)
	defer netns.Close()
	if err != nil {
		return err
	}
	// set eni dev to eni ns
	err = netlink.LinkSetNsFd(eniIntf, int(netns.Fd()))
	if err != nil {
		logger.Errorf(ctx, "set eni to ns error")
		return err
	}

	_ = netns.Do(func(_ ns.NetNS) error {
		eniIntf, err = r.findENILinkByMac(eni.MacAddress)
		if err != nil {
			logger.Errorf(ctx, "find eni: %s , mac: %s error", eni.EniID, eni.MacAddress)
			return err
		}
		// set eni up
		err = r.setLinkUP(ctx, eniIntf)
		if err != nil {
			logger.Errorf(ctx, "set eni up error")
			return err
		}
		//add eni primary ip address
		err = r.addPrimaryIP(ctx, eniIntf, eni.PrimaryIPAddress)
		if err != nil {
			logger.Errorf(ctx, "add primary ip error")
			return err
		}

		err = r.addIPv6Addresses(ctx, eniIntf, eni)
		if err != nil {
			logger.Errorf(ctx, "add ipv6 addresses error")
			return err
		}

		err = r.addENIRule(ctx, eni, eniIntf)
		if err != nil {
			logger.Errorf(ctx, "add scope link route error")
			return err
		}

		err = r.delScopeLinkRoute(ctx, eniIntf)
		if err != nil {
			logger.Errorf(ctx, "del scope link route error")
			return err
		}

		return nil
	})

	return nil
}

func (r *BciNodeReconciler) DelEni(ctx context.Context, eniID string) error {
	// network controllr responsible for no pods are using this eni
	eniNsName := fmt.Sprintf("enins-%s", eniID)
	err := delEniNs(eniNsName)
	if err != nil {
		logger.Errorf(ctx, "delete eni ns error: %s", eniNsName)
		return err
	}
	logger.Infof(ctx, "Delete eni %s successfully", eniNsName)
	return nil
}

func (r *BciNodeReconciler) AddEniWithoutNetns(ctx context.Context, eni *networkingv1.AllocationEni) error {
	eniIntf, err := r.findENILinkByMac(eni.MacAddress)
	if err != nil {
		logger.Errorf(ctx, "find eni: %s , mac: %s error", eni.EniID, eni.MacAddress)
		return err
	}

	// set eni up
	err = r.setLinkUP(ctx, eniIntf)
	if err != nil {
		logger.Errorf(ctx, "set eni up error: %v", err)
		return err
	}
	//add eni primary ip address
	err = r.addPrimaryIP(ctx, eniIntf, eni.PrimaryIPAddress)
	if err != nil {
		logger.Errorf(ctx, "add primary ip error: %v", err)
		return err
	}

	// 添加IPv6地址
	err = r.addIPv6Addresses(ctx, eniIntf, eni)
	if err != nil {
		logger.Errorf(ctx, "add ipv6 addresses error: %v", err)
		return err
	}

	// add unique route table
	err = r.addFromEniRouteTable(ctx, eni, eniIntf)
	if err != nil {
		logger.Errorf(ctx, "add from eni route table: %v", err)
		return err
	}

	// add vpc cidr in main route table
	err = r.addEniVPCRoute(ctx, eni, eniIntf)
	if err != nil {
		logger.Errorf(ctx, "add eni vpc route error: %v", err)
		return err
	}

	return nil
}

func (r *BciNodeReconciler) setLinkUP(ctx context.Context, intf netlink.Link) error {
	if intf.Attrs().Flags&net.FlagUp != 0 {
		logger.Infof(ctx, "link %v is already up", intf.Attrs().Name)
		return nil
	}
	logger.Warningf(ctx, "link %v is down, will bring it up", intf.Attrs().Name)
	err := r.nlink.LinkSetUp(intf)
	if err != nil {
		return err
	}
	logger.Infof(ctx, "set eni up success")
	return nil
}

func (r *BciNodeReconciler) addPrimaryIP(ctx context.Context, intf netlink.Link, primaryIP string) error {
	// primary ip address format: xxx.xxx.xxx.xxx
	mask, err := r.metaclient.GetLinkMask(intf.Attrs().HardwareAddr.String(), primaryIP)
	if err != nil {
		logger.Errorf(ctx, "get primary ip mask from meta client error")
		return err
	}
	logger.Infof(ctx, "primary ip mask: %v", mask)
	addrs, err := r.nlink.AddrList(intf, netlink.FAMILY_V4)
	if err != nil {
		logger.Errorf(ctx, "failed to list addresses of link %v: %v", intf.Attrs().Name, err)
		return err
	}

	for _, addr := range addrs {
		// primary IP already on link
		if addr.IP.String() == primaryIP {
			return nil
		}
	}

	logger.Infof(ctx, "start to add primary IP %v to link %v", primaryIP, intf.Attrs().Name)
	// mask is in prefix format
	addr := &netlink.Addr{
		IPNet: &net.IPNet{
			IP:   net.ParseIP(primaryIP),
			Mask: net.IPMask(net.ParseIP(mask).To4()),
		},
	}

	err = r.nlink.AddrAdd(intf, addr)
	if err != nil && !IsExistsError(err) {
		logger.Errorf(ctx, "failed to add primary IP %v to link %v", addr.String(), intf.Attrs().Name)
		return err
	}

	logger.Infof(ctx, "add primary IP %v to link %v successfully", addr.String(), intf.Attrs().Name)
	return nil
}

func (r *BciNodeReconciler) addIPv6Addresses(ctx context.Context, intf netlink.Link, eni *networkingv1.AllocationEni) error {
	if len(eni.PrivateIPv6Addresses) == 0 {
		logger.Infof(ctx, "no IPv6 addresses to add for eni %v", eni.EniID)
		return nil
	}

	// 获取现有的IPv6地址
	existingAddrs, err := r.nlink.AddrList(intf, netlink.FAMILY_V6)
	if err != nil {
		return fmt.Errorf("failed to list IPv6 addresses: %v", err)
	}

	// 创建现有地址的映射
	existingIPv6Addrs := make(map[string]bool)
	for _, addr := range existingAddrs {
		if addr.IP.To4() == nil { // 只处理IPv6地址
			existingIPv6Addrs[addr.IP.String()] = true
		}
	}

	// 添加IPv6地址
	for ipv6Addr := range eni.PrivateIPv6Addresses {
		if existingIPv6Addrs[ipv6Addr] {
			logger.Infof(ctx, "IPv6 address %v already exists on interface %v", ipv6Addr, intf.Attrs().Name)
			continue
		}

		// 解析IPv6地址和前缀长度
		ip, ipNet, err := net.ParseCIDR(ipv6Addr)
		if err != nil {
			// 如果不是CIDR格式，尝试添加/128前缀
			ip = net.ParseIP(ipv6Addr)
			if ip == nil {
				logger.Errorf(ctx, "invalid IPv6 address format: %v", ipv6Addr)
				continue
			}
			ipNet = &net.IPNet{
				IP:   ip,
				Mask: net.CIDRMask(128, 128), // /128 for single IPv6 address
			}
		}

		addr := &netlink.Addr{
			IPNet: ipNet,
		}

		err = r.nlink.AddrAdd(intf, addr)
		if err != nil && !IsExistsError(err) {
			logger.Errorf(ctx, "failed to add IPv6 address %v to interface %v: %v", ipv6Addr, intf.Attrs().Name, err)
			continue
		}

		logger.Infof(ctx, "added IPv6 address %v to interface %v", ipv6Addr, intf.Attrs().Name)
	}

	return nil
}

func (r *BciNodeReconciler) delScopeLinkRoute(ctx context.Context, intf netlink.Link) error {
	addrs, err := r.nlink.AddrList(intf, netlink.FAMILY_V4)
	if err != nil {
		return err
	}

	for _, addr := range addrs {
		dst := net.IPNet{
			IP:   addr.IP.Mask(addr.Mask),
			Mask: addr.Mask,
		}
		err = r.nlink.RouteDel(&netlink.Route{
			Dst:       &dst,
			Scope:     netlink.SCOPE_LINK,
			LinkIndex: intf.Attrs().Index,
		})
		if err != nil && !IsNotExistError(err) {
			return err
		}
	}

	return nil
}

func (r *BciNodeReconciler) addENIRule(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	var gateway net.IP
	defaultRt, err := r.findLinkDefaultRoute(ctx, intf)
	if err != nil {
		logger.Infof(ctx, "failed to get gateway of eni %v from link default route: %v", eni.EniID, err)
		logger.Infof(ctx, "fall back to get gateway of eni %v from meta-data", eni.EniID)
		// fallback to meta-data api
		// TODO: check primary address mask by metaclient or by prefix from network controller
		gw, err := r.getLinkGateway(ctx, eni.MacAddress, eni.PrimaryIPAddress)
		if err != nil {
			logger.Errorf(ctx, "failed to get gateway of eni %v from meta-data: %v", eni.EniID, err)
			return err
		}
		gateway = gw
	} else {
		gateway = defaultRt.Gw
	}

	logger.Infof(ctx, "eni %v with primary IP %v has gateway: %v", eni.EniID, eni.PrimaryIPAddress, gateway)

	// 添加 IPv4 默认路由
	_, IPv4ZeroCIDR, _ := net.ParseCIDR("0.0.0.0/0")
	rt := &netlink.Route{
		LinkIndex: intf.Attrs().Index,
		Dst:       IPv4ZeroCIDR,
		Gw:        gateway,
	}
	// rt.SetFlag(netlink.FLAG_ONLINK)
	err = r.nlink.RouteAdd(rt)
	if err != nil {
		msg := fmt.Sprintf("failed to add IPv4 default route %+v: %v", *rt, err)
		logger.Error(ctx, msg)
		return err
	}
	logger.Infof(ctx, "add IPv4 default route %+v successfully", *rt)

	// 添加 IPv6 默认路由（动态获取网关）
	err = r.addIPv6DefaultRoute(ctx, eni, intf)
	if err != nil {
		logger.Errorf(ctx, "failed to add IPv6 default route for eni %v: %v", eni.EniID, err)
		return err
	}

	return nil
}

func (r *BciNodeReconciler) addIPv6DefaultRoute(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	if len(eni.PrivateIPv6Addresses) == 0 {
		logger.Infof(ctx, "no IPv6 addresses to add for eni %v", eni.EniID)
		return nil
	}

	// 获取 IPv6 网关
	ipv6Gateway, err := r.getIPv6GatewayWithFallback(ctx, eni, intf)
	if err != nil {
		logger.Errorf(ctx, "failed to get IPv6 gateway for eni %v: %v", eni.EniID, err)
		return err
	}

	_, IPv6ZeroCIDR, _ := net.ParseCIDR("::/0")
	rtv6 := &netlink.Route{
		LinkIndex: intf.Attrs().Index,
		Dst:       IPv6ZeroCIDR,
		Gw:        ipv6Gateway,
	}

	err = r.nlink.RouteAdd(rtv6)
	if err != nil {
		logger.Error(ctx, "failed to add IPv6 default route %+v: %v", *rtv6, err)
		// 如果添加失败，尝试替换
		err = r.nlink.RouteReplace(rtv6)
		if err != nil {
			msg := fmt.Sprintf("failed to add/replace IPv6 default route %+v: %v", *rtv6, err)
			logger.Error(ctx, msg)
			return err
		}
	}

	logger.Infof(ctx, "add IPv6 default route %+v successfully", *rtv6)
	return nil
}

// isIPv6GatewayReachable 检查IPv6网关是否可达
func (r *BciNodeReconciler) isIPv6GatewayReachable(ctx context.Context, intf netlink.Link, gateway net.IP) bool {
	// 检查网关是否为链路本地地址
	if gateway.IsLinkLocalUnicast() {
		logger.Infof(ctx, "gateway %v is link-local, considering reachable", gateway)
		return true
	}

	// 检查是否有到网关的路由
	routes, err := r.nlink.RouteList(intf, netlink.FAMILY_V6)
	if err != nil {
		logger.Errorf(ctx, "failed to list IPv6 routes for interface %v: %v", intf.Attrs().Name, err)
		return false
	}

	for _, route := range routes {
		if route.Dst != nil && route.Dst.Contains(gateway) {
			logger.Infof(ctx, "found route to gateway %v via %v", gateway, route)
			return true
		}
	}

	logger.Warningf(ctx, "no route found to gateway %v", gateway)
	return false
}

func (r *BciNodeReconciler) getIPv6GatewayWithFallback(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) (net.IP, error) {
	// 首先尝试从 IPv6 地址中获取网关
	if len(eni.PrivateIPv6Addresses) > 0 {
		// 获取第一个 IPv6 地址作为示例来查询网关
		for ipv6Addr := range eni.PrivateIPv6Addresses {
			gateway, err := r.getLinkGateway(ctx, eni.MacAddress, ipv6Addr)
			if err == nil && gateway != nil {
				// 验证返回的是 IPv6 网关
				if gateway.To4() == nil {
					logger.Infof(ctx, "found IPv6 gateway %v from meta-data for eni %v", gateway, eni.EniID)
					return gateway, nil
				}
			}
		}
	}

	// 尝试从网络接口的 IPv6 默认路由中获取网关
	ipv6DefaultRt, err := r.findLinkIPv6DefaultRoute(ctx, intf)
	if err == nil && ipv6DefaultRt.Gw != nil && ipv6DefaultRt.Gw.To4() == nil {
		logger.Infof(ctx, "found IPv6 gateway %v from link default route for eni %v", ipv6DefaultRt.Gw, eni.EniID)
		return ipv6DefaultRt.Gw, nil
	}

	// 尝试从IPv6地址自动计算网关
	ipv6Gateway, err := r.calculateIPv6GatewayFromAddresses(ctx, eni, intf)
	if err == nil && ipv6Gateway != nil {
		logger.Infof(ctx, "calculated IPv6 gateway %v for eni %v", ipv6Gateway, eni.EniID)
		return ipv6Gateway, nil
	}

	// 最后回退到链路本地网关
	logger.Infof(ctx, "falling back to link-local gateway fe80::1 for eni %v", eni.EniID)
	return net.ParseIP("fe80::1"), nil
}

// calculateIPv6GatewayFromAddresses 从IPv6地址计算网关
func (r *BciNodeReconciler) calculateIPv6GatewayFromAddresses(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) (net.IP, error) {
	if len(eni.PrivateIPv6Addresses) == 0 {
		return nil, fmt.Errorf("no IPv6 addresses available")
	}

	// 获取接口的IPv6地址
	addrs, err := r.nlink.AddrList(intf, netlink.FAMILY_V6)
	if err != nil {
		return nil, fmt.Errorf("failed to list IPv6 addresses: %v", err)
	}

	for _, addr := range addrs {
		if addr.IP.To4() != nil {
			continue // 跳过IPv4地址
		}

		// 检查是否是全局单播地址
		if addr.IP.IsGlobalUnicast() {
			// 计算网关地址（通常是子网中的第一个地址）
			gateway := r.calculateIPv6Gateway(addr.IP, addr.Mask)
			if gateway != nil {
				logger.Infof(ctx, "calculated gateway %v from address %v", gateway, addr.IP)
				return gateway, nil
			}
		}
	}

	return nil, fmt.Errorf("no suitable IPv6 address found for gateway calculation")
}

// calculateIPv6Gateway 从IPv6地址和掩码计算网关
func (r *BciNodeReconciler) calculateIPv6Gateway(ip net.IP, mask net.IPMask) net.IP {
	if ip.To4() != nil {
		return nil // 不是IPv6地址
	}

	// 创建子网
	subnet := &net.IPNet{
		IP:   ip.Mask(mask),
		Mask: mask,
	}

	// 网关通常是子网中的第一个地址
	gateway := make(net.IP, len(subnet.IP))
	copy(gateway, subnet.IP)

	// 将最后一个字节设置为1（网关通常是.1）
	if len(gateway) > 0 {
		gateway[len(gateway)-1] = 1
	}

	return gateway
}

func (r *BciNodeReconciler) findLinkIPv6DefaultRoute(ctx context.Context, intf netlink.Link) (*netlink.Route, error) {
	// ip -6 route show dev ethX
	routes, err := r.nlink.RouteList(intf, netlink.FAMILY_V6)
	if err != nil {
		return nil, fmt.Errorf("failed to list IPv6 dev %v routes: %w", intf.Attrs().Name, err)
	}
	// find eni IPv6 default route
	for _, r := range routes {
		if r.Dst == nil || r.Dst.String() == "::/0" {
			return &r, nil
		}
	}

	return nil, fmt.Errorf("IPv6 default route of %v not found", intf.Attrs().Name)
}

func (r *BciNodeReconciler) addFromEniRouteTable(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	eniInterfaceSuffix, err := enilink.GetEniLinkNameSuffix(intf)
	if err != nil {
		return err
	}

	rtTable := enilink.EniRouteTableBase + eniInterfaceSuffix
	logger.Infof(ctx, "eni %v has route table index: %v", eni.EniID, rtTable)

	gateway, err := r.getEniGatewayWithFallback(ctx, eni, intf)
	if err != nil {
		return err
	}

	_, IPv4ZeroCIDR, _ := net.ParseCIDR("0.0.0.0/0")
	rt := &netlink.Route{
		LinkIndex: intf.Attrs().Index,
		Dst:       IPv4ZeroCIDR,
		Gw:        gateway,
		Table:     rtTable,
	}
	rt.SetFlag(netlink.FLAG_ONLINK)

	err = r.nlink.RouteReplace(rt)
	if err != nil {
		logger.Errorf(ctx, "failed to replace eni route table: %v", err)
		return err
	}

	return nil
}

func (r *BciNodeReconciler) getEniGatewayWithFallback(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) (net.IP, error) {
	var (
		gateway  net.IP
		err      error
		finalErr = errors.New("failed to get eni gateway via meta-data and netlink")
	)

	gateway, err = r.getLinkGateway(ctx, eni.MacAddress, eni.PrimaryIPAddress)
	if err != nil {
		logger.Errorf(ctx, "failed to get get gateway of eni %v from meta-data: %v, try to fallback", eni.EniID, err)
		rt, err := r.findLinkDefaultRoute(ctx, intf)
		if err != nil {
			logger.Errorf(ctx, "failed to get gateway of eni %v from link default route: %v", eni.EniID, err)
			return nil, finalErr
		}
		gateway = rt.Gw
	}

	if gateway == nil {
		return nil, finalErr
	}

	return gateway, nil
}

func (r *BciNodeReconciler) addEniVPCRoute(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	_, vpcCIDR, err := net.ParseCIDR(eni.VpcCIDR)
	if err != nil {
		return err
	}

	gateway, err := r.getEniGatewayWithFallback(ctx, eni, intf)
	if err != nil {
		return err
	}

	err = r.nlink.RouteReplace(&netlink.Route{
		Gw:        gateway,
		LinkIndex: intf.Attrs().Index,
		Dst:       vpcCIDR,
	})
	if err != nil {
		return err
	}

	return nil
}

func (r *BciNodeReconciler) findLinkDefaultRoute(ctx context.Context, intf netlink.Link) (*netlink.Route, error) {
	// ip route show dev ethX
	routes, err := r.nlink.RouteList(intf, netlink.FAMILY_V4)
	if err != nil {
		return nil, fmt.Errorf("failed to list dev %v routes: %w", intf.Attrs().Name, err)
	}
	// find eni default route
	for _, r := range routes {
		if r.Dst == nil || r.Dst.String() == "0.0.0.0/0" {
			return &r, nil
		}
	}

	return nil, fmt.Errorf("default route of %v not found", intf.Attrs().Name)
}

func (r *BciNodeReconciler) getLinkGateway(ctx context.Context, macAddress string, ip string) (net.IP, error) {
	gateway, err := r.metaclient.GetLinkGateway(macAddress, ip)
	if err != nil {
		return nil, err
	}

	gw := net.ParseIP(gateway)
	if gw == nil {
		return nil, fmt.Errorf("error parsing gateway IP address: %v", gateway)
	}
	return gw, nil
}

func IsExistsError(err error) bool {
	if errno, ok := err.(syscall.Errno); ok {
		return errno == syscall.EEXIST
	}
	return false
}

func IsNotExistError(err error) bool {
	if errno, ok := err.(syscall.Errno); ok {
		return errno == syscall.ENOENT || errno == syscall.ESRCH
	}

	return false
}
